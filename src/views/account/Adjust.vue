<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import { usePermission } from "../../composables/usePermission";
import { isoFormatYYmm, formatDateTime } from "../../utils/common";
import type {
  AdjustDetailItem,
  AdjustDetailParams,
  ChargeAdjustItem,
  ChargeAdjustParams,
} from "../../types/adjustDetail";
import {
  getAdjustDetails,
  exportAdjustDetails,
  getChargeAdjustList,
} from "../../services/adjustDetail";
import {
  adjustStateMap,
  adjustStateSeverityMap,
  adjustTypeMap,
} from "../../utils/const";
import AdjustAccountDrawer from "./AdjustAccountDrawer.vue";

const toast = useToast();
const loading = ref(false);
const totalRecords = ref(0);
const exporting = ref(false);

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// Tab相关状态
const activeTab = ref<string | number>("records");

// 调账记录相关状态
const adjustDetails = ref<AdjustDetailItem[]>([]);

// 权责调账相关状态
const chargeAdjustList = ref<ChargeAdjustItem[]>([]);
const chargeAdjustLoading = ref(false);
const chargeAdjustTotalRecords = ref(0);

// 调账弹框相关状态
const adjustDialogVisible = ref(false);
const currentAdjustItem = ref<ChargeAdjustItem | null>(null);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 权责调账分页参数
const chargeAdjustLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const filterOrderNo = ref("");
const filterAdjustType = ref<number | null>(null);
const filterChargeMonth = ref<Date | null>(null);
const filterAdjustMonth = ref<Date | null>(null);

// 权责调账筛选参数
const chargeAdjustFilterOrderNo = ref("");
const chargeAdjustFilterChargeMonth = ref<Date | null>(null);

// 调账类别选项
const adjustTypeOptions = ref([
  { label: "权责调账", value: 1 },
  { label: "无权责调账", value: 2 },
  { label: "外部费用导入调账", value: 3 },
]);

// 加载调账记录列表数据
const loadAdjustDetails = async () => {
  try {
    loading.value = true;
    const params: AdjustDetailParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (filterOrderNo.value) {
      params.order_no = filterOrderNo.value;
    }
    if (filterAdjustType.value) {
      params.adjust_type = filterAdjustType.value;
    }
    if (filterChargeMonth.value) {
      params.charge_month = isoFormatYYmm(filterChargeMonth.value);
    }
    if (filterAdjustMonth.value) {
      params.adjust_month = isoFormatYYmm(filterAdjustMonth.value);
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === "" || params[key] === null) {
        delete params[key];
      }
    });

    const response = await getAdjustDetails(params);
    adjustDetails.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载调账记录失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadAdjustDetails();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadAdjustDetails();
};

// 重置筛选
const resetFilters = () => {
  filterOrderNo.value = "";
  filterAdjustType.value = null;
  filterChargeMonth.value = null;
  filterAdjustMonth.value = null;
  lazyParams.value.page = 1;
  loadAdjustDetails();
};

// 格式化账期
const formatChargeMonth = (month: number | null | undefined) => {
  if (!month) return "--";
  const monthStr = month.toString();
  if (monthStr.length === 6) {
    return `${monthStr.substring(0, 4)}-${monthStr.substring(4, 6)}`;
  }
  return monthStr;
};

// 导出调账明细列表
const handleExport = async () => {
  try {
    exporting.value = true;

    // 构建导出参数，包含当前的筛选条件
    const exportParams: AdjustDetailParams = {};

    if (filterOrderNo.value) {
      exportParams.order_no = filterOrderNo.value;
    }
    if (filterAdjustType.value) {
      exportParams.adjust_type = filterAdjustType.value;
    }
    if (filterChargeMonth.value) {
      exportParams.charge_month = isoFormatYYmm(filterChargeMonth.value);
    }
    if (filterAdjustMonth.value) {
      exportParams.adjust_month = isoFormatYYmm(filterAdjustMonth.value);
    }

    // 调用导出方法
    const response = await exportAdjustDetails(exportParams);

    // 创建下载链接
    const url = window.URL.createObjectURL(response.blob);
    const link = document.createElement("a");
    link.href = url;

    // 从响应中获取文件名，如果没有则使用默认值
    link.download = response.filename || "调账明细.xlsx";

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "文件导出成功",
      life: 3000,
    });
  } catch (error) {
    console.error("Export failed:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "文件导出失败",
      life: 3000,
    });
  } finally {
    exporting.value = false;
  }
};

// 加载权责调账列表数据
const loadChargeAdjustList = async () => {
  try {
    chargeAdjustLoading.value = true;
    const params: ChargeAdjustParams = {
      page: chargeAdjustLazyParams.value.page,
      pageSize: chargeAdjustLazyParams.value.pageSize,
    };

    if (chargeAdjustFilterOrderNo.value) {
      params.order_no = chargeAdjustFilterOrderNo.value;
    }
    if (chargeAdjustFilterChargeMonth.value) {
      params.charge_month = isoFormatYYmm(chargeAdjustFilterChargeMonth.value);
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === "" || params[key] === null) {
        delete params[key];
      }
    });

    const response = await getChargeAdjustList(params);
    chargeAdjustList.value = response.data.records;
    chargeAdjustTotalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载权责调账列表失败",
      life: 3000,
    });
  } finally {
    chargeAdjustLoading.value = false;
  }
};

// 处理权责调账分页事件
const onChargeAdjustPage = (event: { page: number; rows: number }) => {
  chargeAdjustLazyParams.value.page = event.page + 1;
  chargeAdjustLazyParams.value.pageSize = event.rows;
  loadChargeAdjustList();
};

// 权责调账搜索
const handleChargeAdjustSearch = () => {
  chargeAdjustLazyParams.value.page = 1;
  loadChargeAdjustList();
};

// 重置权责调账筛选
const resetChargeAdjustFilters = () => {
  chargeAdjustFilterOrderNo.value = "";
  chargeAdjustFilterChargeMonth.value = null;
  chargeAdjustLazyParams.value.page = 1;
  loadChargeAdjustList();
};

// 权责调账操作
const handleChargeAdjust = (item: ChargeAdjustItem) => {
  currentAdjustItem.value = item;
  adjustDialogVisible.value = true;
};

// 调账成功回调
const handleAdjustSuccess = () => {
  // 重新加载权责调账列表
  loadChargeAdjustList();
  // 重新加载调账记录列表
  loadAdjustDetails();
};

// Tab切换处理
const handleTabChange = (tabValue: string | number) => {
  activeTab.value = tabValue;
  // 根据不同Tab加载不同数据
  if (tabValue === "records") {
    loadAdjustDetails();
  } else if (tabValue === "responsibility") {
    loadChargeAdjustList();
  }
  // TODO: 实现其他Tab的数据加载
};

onMounted(async () => {
  await initializeUserInfo();
  loadAdjustDetails();
});
</script>

<template>
  <div class="adjust-container">
    <div class="card">
      <!-- PrimeVue Tabs组件 -->
      <Tabs :value="activeTab" @update:value="handleTabChange" class="mb-4">
        <TabList>
          <Tab value="records">调账记录</Tab>
          <Tab value="responsibility">权责调账</Tab>
          <Tab value="non-responsibility">无权责调账</Tab>
        </TabList>

        <TabPanels>
          <!-- 调账记录Tab内容 -->
          <TabPanel value="records">
            <!-- 筛选工具栏 -->
            <Toolbar class="mb-2">
              <template #end>
                <div class="flex flex-wrap align-items-center gap-2">
                  <FloatLabel>
                    <Select
                      v-model="filterAdjustType"
                      :options="adjustTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择调账类别"
                      showClear
                      style="min-width: 15rem"
                    />
                  </FloatLabel>
                  <FloatLabel>
                    <InputText v-model="filterOrderNo" />
                    <label>订单编号</label>
                  </FloatLabel>
                  <FloatLabel>
                    <DatePicker
                      v-model="filterChargeMonth"
                      view="month"
                      dateFormat="yymm"
                      showIcon
                    />
                    <label>账期</label>
                  </FloatLabel>
                  <FloatLabel class="mr-2">
                    <DatePicker
                      v-model="filterAdjustMonth"
                      view="month"
                      dateFormat="yymm"
                      showIcon
                    />
                    <label>调整账期</label>
                  </FloatLabel>
                </div>
                <Button
                  label="搜索"
                  icon="pi pi-search"
                  @click="handleSearch"
                  class="mr-2 button-primary"
                />
                <Button
                  label="重置"
                  icon="pi pi-refresh"
                  severity="secondary"
                  outlined
                  @click="resetFilters"
                  class="button-secondary"
                />
                <Divider layout="vertical" />
                <Button
                  icon="pi pi-file-export"
                  @click="handleExport"
                  severity="help"
                  class="button-primary"
                  :loading="exporting"
                  :disabled="exporting"
                  v-tooltip.top="'导出Excel文件'"
                />
              </template>
            </Toolbar>

            <!-- 数据表格 -->
            <DataTable
              :value="adjustDetails"
              :lazy="true"
              :paginator="true"
              :rows="20"
              :rowsPerPageOptions="[10, 20, 50]"
              :totalRecords="totalRecords"
              :loading="loading"
              @page="onPage($event)"
              showGridlines
              stripedRows
              scrollable
              scrollHeight="calc(100vh - 26.5rem)"
            >
              <template #empty>
                <div class="empty-message">
                  <i
                    class="pi pi-inbox"
                    style="
                      font-size: 2rem;
                      color: var(--p-text-color-secondary);
                      margin-bottom: 1rem;
                    "
                  ></i>
                  <p>暂无调账记录数据</p>
                </div>
              </template>
              <Column
                field="order_no"
                header="订单编号"
                style="min-width: 15rem"
              />
              <Column
                field="sub_order_no"
                header="子订单编号"
                style="min-width: 18rem"
              />
              <Column
                field="charge_month"
                header="账期"
                style="min-width: 8rem"
              >
                <template #body="slotProps">
                  {{ formatChargeMonth(slotProps.data.charge_month) }}
                </template>
              </Column>
              <Column
                field="adjust_month"
                header="调整账期"
                style="min-width: 10rem"
              >
                <template #body="slotProps">
                  {{ formatChargeMonth(slotProps.data.adjust_month) }}
                </template>
              </Column>
              <Column
                field="adjust_amount"
                header="调账金额"
                style="min-width: 10rem"
              >
                <template #body="slotProps">
                  {{ slotProps.data.adjust_amount.toLocaleString() }}
                </template>
              </Column>
              <Column
                field="adjust_reason_class"
                header="调账原因分类"
                style="min-width: 12rem"
              />
              <Column
                field="adjust_reason"
                header="调账原因"
                style="min-width: 12rem"
              />
              <Column
                field="adjust_type"
                header="调账类别"
                style="min-width: 10rem"
              >
                <template #body="slotProps">
                  {{ adjustTypeMap[slotProps.data.adjust_type] }}
                </template>
              </Column>
              <Column field="state" header="状态" style="min-width: 8rem">
                <template #body="slotProps">
                  <Tag
                    :value="adjustStateMap[slotProps.data.state]"
                    :severity="adjustStateSeverityMap[slotProps.data.state]"
                  />
                </template>
              </Column>
              <Column
                field="created_at"
                header="创建时间"
                style="min-width: 15rem"
              >
                <template #body="slotProps">
                  {{ formatDateTime(slotProps.data.created_at) }}
                </template>
              </Column>
              <Column
                header="操作"
                style="min-width: 8rem"
                frozen
                alignFrozen="right"
              >
                <template #body="">
                  <Button
                    icon="pi pi-check-circle"
                    severity="help"
                    outlined
                    rounded
                    :disabled="!hasOperationPermission"
                    v-tooltip.top="'审批通过'"
                    class="mr-2"
                  />
                  <Button
                    icon="pi pi-times"
                    severity="danger"
                    outlined
                    rounded
                    :disabled="!hasOperationPermission"
                    v-tooltip.top="'审批拒绝'"
                  />
                </template>
              </Column>
            </DataTable>
          </TabPanel>

          <!-- 权责调账Tab内容 -->
          <TabPanel value="responsibility">
            <!-- 筛选工具栏 -->
            <Toolbar class="mb-2">
              <template #end>
                <div class="flex flex-wrap align-items-center gap-2">
                  <FloatLabel>
                    <InputText v-model="chargeAdjustFilterOrderNo" />
                    <label>订单编号</label>
                  </FloatLabel>
                  <FloatLabel class="mr-2">
                    <DatePicker
                      v-model="chargeAdjustFilterChargeMonth"
                      view="month"
                      dateFormat="yymm"
                      showIcon
                    />
                    <label>账期</label>
                  </FloatLabel>
                </div>
                <Button
                  label="搜索"
                  icon="pi pi-search"
                  @click="handleChargeAdjustSearch"
                  class="mr-2 button-primary"
                />
                <Button
                  label="重置"
                  icon="pi pi-refresh"
                  severity="secondary"
                  outlined
                  @click="resetChargeAdjustFilters"
                  class="button-secondary"
                />
              </template>
            </Toolbar>

            <!-- 数据表格 -->
            <DataTable
              :value="chargeAdjustList"
              :lazy="true"
              :paginator="true"
              :rows="20"
              :rowsPerPageOptions="[10, 20, 50]"
              :totalRecords="chargeAdjustTotalRecords"
              :loading="chargeAdjustLoading"
              @page="onChargeAdjustPage($event)"
              showGridlines
              stripedRows
              scrollable
              scrollHeight="calc(100vh - 26.5rem)"
            >
              <template #empty>
                <div class="empty-message">
                  <i
                    class="pi pi-inbox"
                    style="
                      font-size: 2rem;
                      color: var(--p-text-color-secondary);
                      margin-bottom: 1rem;
                    "
                  ></i>
                  <p>暂无权责调账数据</p>
                </div>
              </template>

              <Column
                field="order_no"
                header="订单编号"
                style="min-width: 15rem"
              />
              <Column
                field="sub_order_no"
                header="子订单编号"
                style="min-width: 18rem"
              />
              <Column
                field="charge_month"
                header="账期"
                style="min-width: 8rem"
              >
                <template #body="slotProps">
                  {{ formatChargeMonth(slotProps.data.charge_month) }}
                </template>
              </Column>
              <Column
                field="fee_amount"
                header="费用金额"
                style="min-width: 10rem"
              >
                <template #body="slotProps">
                  {{ slotProps.data.fee_amount.toLocaleString() }}
                </template>
              </Column>
              <Column
                field="income_type"
                header="收入类型"
                style="min-width: 10rem"
              />
              <Column
                field="tax_type"
                header="税收类型"
                style="min-width: 10rem"
              />
              <Column
                field="customer_name"
                header="客户名称"
                style="min-width: 15rem"
              />
              <Column
                header="操作"
                style="min-width: 8rem"
                frozen
                alignFrozen="right"
              >
                <template #body="slotProps">
                  <Button
                    label="调账"
                    icon="pi pi-pencil"
                    severity="info"
                    outlined
                    size="small"
                    @click="handleChargeAdjust(slotProps.data)"
                    :disabled="!hasOperationPermission"
                  />
                </template>
              </Column>
            </DataTable>
          </TabPanel>

          <!-- 无权责调账Tab内容 -->
          <TabPanel value="non-responsibility">
            <div class="empty-message">
              <i
                class="pi pi-info-circle"
                style="
                  font-size: 2rem;
                  color: var(--p-text-color-secondary);
                  margin-bottom: 1rem;
                "
              ></i>
              <p>无权责调账功能开发中...</p>
            </div>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>

    <!-- 调账弹框 -->
    <AdjustAccountDrawer
      v-model:visible="adjustDialogVisible"
      :chargeAdjustItem="currentAdjustItem"
      @success="handleAdjustSuccess"
    />
  </div>
</template>

<style scoped>
.adjust-container {
  padding: 1rem;
  background: #f8f9fa;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* PrimeVue Tabs样式优化 */
:deep(.p-tabs) {
  margin-bottom: 0.5rem;
}

:deep(.p-tabs .p-tablist) {
  border-radius: 8px;
  padding: 0.25rem;
  border: none;
  width: fit-content;
}

:deep(.p-tabs .p-tab) {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: #6e6e73;
  margin: 0 0.125rem;
}

:deep(.p-tabs .p-tab:hover:not(.p-tab-active)) {
  background: rgba(255, 255, 255, 0.5);
  color: #1d1d1f;
}

:deep(.p-tabs .p-tab.p-tab-active) {
  background: white;
  color: #1d1d1f;
  font-weight: 600;
}

:deep(.p-tabs .p-tabpanels) {
  padding: 0;
  background: transparent;
  border: none;
}

.button-primary {
  color: white;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.button-primary:hover {
  transform: translateY(-1px);
}

.button-secondary {
  color: #6e6e73;
  border-color: #d2d2d7;
  background: white;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.button-secondary:hover {
  background: #f5f5f7;
  border-color: #a1a1a6;
  color: #1d1d1f;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--p-surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--p-text-color-secondary);
  font-size: 1.1rem;
}

/* DataTable样式优化 */
:deep(.p-datatable) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

:deep(.p-datatable .p-datatable-header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

/* Tag样式优化 */
:deep(.p-tag) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Toolbar样式优化 */
:deep(.p-toolbar) {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .adjust-container {
    padding: 0.5rem;
  }

  .card {
    padding: 1rem;
    border-radius: 8px;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .filter-section .p-float-label {
    min-width: auto;
    width: 100%;
  }

  :deep(.p-tabs .p-tablist) {
    width: 100%;
    justify-content: center;
  }

  :deep(.p-tabs .p-tab) {
    flex: 1;
    text-align: center;
  }
}

@media (max-width: 480px) {
  :deep(.p-tabs .p-tablist) {
    flex-direction: column;
    gap: 0.5rem;
    background: transparent;
    padding: 0;
    width: 100%;
  }

  :deep(.p-tabs .p-tab) {
    border: 1px solid #d2d2d7;
    background: white;
    margin: 0;
  }

  :deep(.p-tabs .p-tab.p-tab-active) {
    background: #007aff;
    color: white;
    border-color: #007aff;
    box-shadow: none;
  }
}
</style>
